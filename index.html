<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>慧习作 - 家长端</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: #FFFFFF;
            width: 375px;
            height: 844px;
            margin: 0 auto;
            position: relative;
            overflow-x: hidden;
        }

        /* 状态栏 */
        .status-bar {
            height: 40px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            position: relative;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .signal-icon {
            width: 18px;
            height: 10px;
            background: linear-gradient(to right, #000 0%, #000 25%, #666 25%, #666 50%, #999 50%, #999 75%, #ccc 75%);
            border-radius: 1px;
        }

        .wifi-icon {
            width: 15px;
            height: 10px;
            background: radial-gradient(circle at bottom, #000 0%, #000 30%, transparent 30%);
            position: relative;
        }

        .wifi-icon::before {
            content: '';
            position: absolute;
            width: 10px;
            height: 7px;
            background: radial-gradient(circle at bottom, #666 0%, #666 40%, transparent 40%);
            top: 2px;
            left: 2.5px;
        }

        .battery-icon {
            width: 24px;
            height: 12px;
            border: 1px solid #000;
            border-radius: 2px;
            background: linear-gradient(to right, #4CAF50 0%, #4CAF50 80%, #f0f0f0 80%);
            position: relative;
        }

        .battery-icon::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: #000;
            border-radius: 0 1px 1px 0;
        }

        /* 用户信息区域 */
        .user-header {
            height: 52px;
            display: flex;
            align-items: center;
            padding: 0 24px;
            background: #FFFFFF;
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 18px;
            background: #CED0F8;
            border: 1px solid #636AE8;
            margin-right: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .user-avatar::after {
            content: '👩';
        }

        .user-name {
            font-weight: 700;
            font-size: 18px;
            color: #323842;
            line-height: 28px;
        }

        /* 孩子选择区域 */
        .children-section {
            background: #F8F9FA;
            padding: 18px 12px;
            height: 112px;
        }

        .children-list {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 10px;
        }

        .child-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .child-avatar {
            width: 44px;
            height: 44px;
            border-radius: 22px;
            border: 2px solid;
        }

        .child-avatar.active {
            background: #D8CBF5;
            border-color: #7F55E0;
        }

        .child-avatar.child2 {
            background: #BAF3EB;
            border-color: #22CCB2;
        }

        .child-avatar.child3 {
            background: #F8CEDB;
            border-color: #E8618C;
        }

        .add-child {
            background: #FFFFFF;
            border: 2px dashed #7F55E0;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0px 0px 1px 0px rgba(23, 26, 31, 0.07), 0px 0px 2px 0px rgba(23, 26, 31, 0.12);
            font-size: 24px;
            color: #7F55E0;
            font-weight: 300;
        }

        .child-name {
            font-size: 14px;
            color: #323842;
            text-align: center;
        }

        /* 孩子信息卡片 */
        .child-info-card {
            background: #FFFFFF;
            padding: 18px 24px;
            height: 108px;
            position: relative;
        }

        .child-info-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .child-info-avatar {
            width: 38px;
            height: 38px;
            border-radius: 19px;
            background: #D8CBF5;
            border: 1px solid #7F55E0;
            margin-right: 12px;
        }

        .child-basic-info {
            flex: 1;
        }

        .child-info-name {
            font-weight: 700;
            font-size: 12px;
            color: #171A1F;
            margin-bottom: 2px;
        }

        .child-birth {
            font-size: 11px;
            color: #9095A0;
        }

        .child-age {
            font-size: 11px;
            color: #9095A0;
            margin-left: 10px;
        }

        .wechat-icon {
            width: 16px;
            height: 16px;
            background: #E8618C;
            border-radius: 2px;
        }

        .child-school-info {
            display: flex;
            gap: 8px;
            font-size: 14px;
            color: #171A1F;
        }

        /* 学年信息 */
        .semester-info {
            padding: 8px 18px;
            font-weight: 700;
            font-size: 12px;
            color: #171A1F;
            background: #FFFFFF;
        }

        /* 作业列表 */
        .assignment-list {
            background: #FFFFFF;
        }

        .assignment-item {
            padding: 11px 17px;
            border-bottom: 1px solid #BCC1CA;
            position: relative;
        }

        .assignment-header {
            display: flex;
            gap: 12px;
            margin-bottom: 12px;
            font-size: 11px;
            color: #323842;
        }

        .assignment-title {
            font-size: 11px;
            color: #323842;
            margin-bottom: 17px;
        }

        .assignment-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .assignment-time {
            font-size: 8px;
            color: #9095A0;
        }

        .assignment-score {
            display: flex;
            align-items: baseline;
            gap: 2px;
        }

        .score-number {
            font-size: 24px;
            color: #DE3B40;
        }

        .score-unit {
            font-size: 12px;
            color: #DE3B40;
        }

        .upload-btn {
            background: #636AE8;
            color: #FFFFFF;
            border: none;
            border-radius: 4px;
            padding: 1px 8px;
            font-size: 11px;
            height: 20px;
        }

        .arrow-right {
            position: absolute;
            right: 17px;
            top: 12px;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #323842;
            font-size: 12px;
        }

        .arrow-right::after {
            content: '›';
            font-size: 16px;
            color: #323842;
        }

        /* 底部导航 */
        .bottom-nav {
            position: absolute;
            bottom: 8px;
            left: 6px;
            right: 6px;
            height: 48px;
            display: flex;
            background: transparent;
        }

        .nav-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            background: #424955;
            border-radius: 2px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .nav-item.active .nav-icon {
            background: #4850E4;
        }

        /* 导航图标样式 */
        .nav-icon.home::after {
            content: '🏠';
            font-size: 14px;
            color: white;
        }

        .nav-icon.chart::after {
            content: '📊';
            font-size: 14px;
            color: white;
        }

        .nav-icon.folder::after {
            content: '📁';
            font-size: 14px;
            color: white;
        }

        .nav-icon.user::after {
            content: '👤';
            font-size: 14px;
            color: white;
        }

        .nav-item.active {
            position: relative;
        }

        .nav-item.active::after {
            content: '';
            position: absolute;
            top: 2.8px;
            right: 12px;
            width: 12px;
            height: 12px;
            background: #DE3B40;
            border-radius: 5px;
            border: 2px solid #FFFFFF;
        }

        .nav-text {
            font-size: 10px;
            color: #424955;
        }

        .nav-item.active .nav-text {
            color: #4850E4;
            font-weight: 700;
        }

        /* 分隔线 */
        .divider {
            height: 20px;
            background: #F8F9FA;
            border-top: 1px solid #BCC1CA;
            border-bottom: 1px solid #BCC1CA;
        }

        .bottom-line {
            text-align: center;
            padding: 10px 0;
            font-size: 8px;
            color: #9095A0;
            background: #FFFFFF;
        }

        /* 交互效果 */
        .child-item:hover {
            cursor: pointer;
            opacity: 0.8;
        }

        .nav-item:hover {
            cursor: pointer;
            opacity: 0.8;
        }

        .upload-btn:hover {
            cursor: pointer;
            background: #5a5fd6;
        }

        .assignment-item:hover {
            cursor: pointer;
            background: #f8f9fa;
        }

        /* 响应式设计 */
        @media (max-width: 375px) {
            body {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- 状态栏 -->
    <div class="status-bar">
        <div class="status-left">
            <div class="signal-icon"></div>
        </div>
        <div class="status-right">
            <div class="wifi-icon"></div>
            <div class="battery-icon"></div>
        </div>
    </div>

    <!-- 用户信息区域 -->
    <div class="user-header">
        <div class="user-avatar"></div>
        <div class="user-name">Rachel</div>
    </div>

    <!-- 孩子选择区域 -->
    <div class="children-section">
        <div class="children-list">
            <div class="child-item">
                <div class="child-avatar active"></div>
                <div class="child-name">孩子1</div>
            </div>
            <div class="child-item">
                <div class="child-avatar child2"></div>
                <div class="child-name">孩子2</div>
            </div>
            <div class="child-item">
                <div class="child-avatar child3"></div>
                <div class="child-name">孩子3</div>
            </div>
            <div class="child-item">
                <div class="child-avatar add-child">+</div>
                <div class="child-name">孩子</div>
            </div>
        </div>
    </div>

    <!-- 孩子信息卡片 -->
    <div class="child-info-card">
        <div class="child-info-header">
            <div class="child-info-avatar"></div>
            <div class="child-basic-info">
                <div class="child-info-name">孩子1</div>
                <div>
                    <span class="child-birth">2020.06.03</span>
                    <span class="child-age">6岁</span>
                </div>
            </div>
            <div class="wechat-icon"></div>
        </div>
        <div class="child-school-info">
            <span>江头中心小学</span>
            <span>一年一班</span>
            <span>09号</span>
        </div>
    </div>

    <!-- 学年信息 -->
    <div class="semester-info">2025～2026学年 上学期</div>

    <!-- 作业列表 -->
    <div class="assignment-list">
        <div class="assignment-item">
            <div class="assignment-header">
                <span>第三单元</span>
                <span>单元作文</span>
                <span>全命题</span>
            </div>
            <div class="assignment-title">我的植物朋友</div>
            <div class="assignment-footer">
                <div class="assignment-time">2025.09.30 14:59</div>
                <button class="upload-btn">上传作文</button>
            </div>
            <div class="arrow-right"></div>
        </div>

        <div class="divider"></div>

        <div class="assignment-item">
            <div class="assignment-header">
                <span>第二单元</span>
                <span>单元作文</span>
                <span>全命题</span>
            </div>
            <div class="assignment-title">我的植物朋友</div>
            <div class="assignment-footer">
                <div class="assignment-time">2025.09.08 11:22</div>
                <div class="assignment-score">
                    <span class="score-number">28</span>
                    <span class="score-unit">分</span>
                </div>
            </div>
            <div class="arrow-right"></div>
        </div>

        <div class="divider"></div>

        <div class="assignment-item">
            <div class="assignment-header">
                <span>第一单元</span>
                <span>单元作文</span>
                <span>全命题</span>
            </div>
            <div class="assignment-title">我的植物朋友</div>
            <div class="assignment-footer">
                <div class="assignment-time">2025.09.08 11:22</div>
                <div class="assignment-score">
                    <span class="score-number">28</span>
                    <span class="score-unit">分</span>
                </div>
            </div>
            <div class="arrow-right"></div>
        </div>

        <div class="divider"></div>
    </div>

    <!-- 底线 -->
    <div class="bottom-line">------我是有底线的------</div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-item">
            <div class="nav-icon chart"></div>
            <div class="nav-text">成长数据</div>
        </div>
        <div class="nav-item active">
            <div class="nav-icon home"></div>
            <div class="nav-text">首页</div>
        </div>
        <div class="nav-item">
            <div class="nav-icon folder"></div>
            <div class="nav-text">过往作文</div>
        </div>
        <div class="nav-item">
            <div class="nav-icon user"></div>
            <div class="nav-text">个人中心</div>
        </div>
    </div>

    <script>
        // 孩子切换功能
        document.addEventListener('DOMContentLoaded', function() {
            const childItems = document.querySelectorAll('.child-item');
            const childInfoName = document.querySelector('.child-info-name');

            childItems.forEach((item, index) => {
                item.addEventListener('click', function() {
                    // 移除所有active状态
                    document.querySelectorAll('.child-avatar').forEach(avatar => {
                        avatar.classList.remove('active');
                    });

                    // 添加active状态到当前选中的孩子
                    const avatar = this.querySelector('.child-avatar');
                    if (!avatar.classList.contains('add-child')) {
                        avatar.classList.add('active');

                        // 更新孩子信息
                        const childName = this.querySelector('.child-name').textContent;
                        childInfoName.textContent = childName;
                    }
                });
            });

            // 底部导航切换
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.addEventListener('click', function() {
                    // 移除所有active状态
                    navItems.forEach(nav => nav.classList.remove('active'));
                    // 添加active状态到当前点击的导航
                    this.classList.add('active');
                });
            });

            // 上传按钮点击事件
            const uploadBtns = document.querySelectorAll('.upload-btn');
            uploadBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    alert('上传作文功能');
                });
            });

            // 作业项点击事件
            const assignmentItems = document.querySelectorAll('.assignment-item');
            assignmentItems.forEach(item => {
                item.addEventListener('click', function() {
                    const title = this.querySelector('.assignment-title').textContent;
                    alert(`查看作业：${title}`);
                });
            });
        });
    </script>
</body>
</html>
