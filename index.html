<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>慧习作 - 家长端</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: #FFFFFF;
            color: #323842;
            max-width: 375px;
            margin: 0 auto;
            min-height: 100vh;
            position: relative;
        }

        /* 顶部用户信息区域 */
        .header {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            height: 52px;
            margin-top: 40px;
            border-bottom: 1px solid #BCC1CA;
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 18px;
            background: #CED0F8;
            border: 1px solid #636AE8;
            overflow: hidden;
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .user-name {
            margin-left: 14px;
            font-weight: 700;
            font-size: 18px;
            line-height: 28px;
            color: #323842;
        }

        /* 状态栏区域 */
        .status-bar {
            height: 40px;
            background: transparent;
            border-bottom: 1px solid #BCC1CA;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 30px;
        }

        .signal-icons {
            display: flex;
            gap: 2px;
        }

        .signal-bar {
            width: 2px;
            background: #000;
        }

        .battery-icon {
            width: 24px;
            height: 12px;
            border: 1px solid #000;
            border-radius: 2px;
            position: relative;
        }

        /* 孩子选择区域 */
        .children-section {
            background: #F8F9FA;
            padding: 18px 12px;
            height: 112px;
        }

        .children-list {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .child-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }

        .child-avatar {
            width: 44px;
            height: 44px;
            border-radius: 22px;
            border: 2px solid;
            overflow: hidden;
        }

        .child-avatar.child1 {
            background: #D8CBF5;
            border-color: #7F55E0;
        }

        .child-avatar.child2 {
            background: #BAF3EB;
            border-color: #22CCB2;
        }

        .child-avatar.child3 {
            background: #F8CEDB;
            border-color: #E8618C;
        }

        .child-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .child-name {
            font-size: 14px;
            line-height: 22px;
            color: #323842;
            text-align: center;
        }

        .add-child {
            width: 44px;
            height: 44px;
            border-radius: 22px;
            background: #FFFFFF;
            border: 1px dashed #7F55E0;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0px 0px 1px 0px rgba(23, 26, 31, 0.07), 0px 0px 2px 0px rgba(23, 26, 31, 0.12);
        }

        .add-icon {
            width: 24px;
            height: 24px;
            color: #7F55E0;
        }

        /* 学生信息卡片 */
        .student-info {
            background: #FFFFFF;
            padding: 18px 24px;
            height: 108px;
            border-bottom: 1px solid #BCC1CA;
        }

        .student-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 18px;
        }

        .student-avatar {
            width: 38px;
            height: 38px;
            border-radius: 19px;
            background: #D8CBF5;
            border: 1px solid #7F55E0;
            overflow: hidden;
        }

        .student-details {
            flex: 1;
        }

        .student-name {
            font-weight: 700;
            font-size: 12px;
            line-height: 20px;
            color: #171A1F;
        }

        .student-birth {
            font-size: 11px;
            line-height: 18px;
            color: #9095A0;
        }

        .student-age {
            font-size: 11px;
            line-height: 18px;
            color: #9095A0;
            margin-left: 10px;
        }

        .wechat-icon {
            width: 16px;
            height: 16px;
            color: #E8618C;
        }

        .school-info {
            display: flex;
            gap: 8px;
            font-size: 14px;
            line-height: 22px;
            color: #171A1F;
        }

        /* 学年信息 */
        .semester-info {
            padding: 8px 18px;
            font-weight: 700;
            font-size: 12px;
            line-height: 20px;
            color: #171A1F;
            background: #FFFFFF;
        }

        /* 分隔线 */
        .divider {
            height: 34px;
            background: #F8F9FA;
            border-bottom: 1px solid #BCC1CA;
        }

        /* 作文列表 */
        .essay-list {
            background: #FFFFFF;
        }

        .essay-item {
            padding: 11px 17px;
            border-bottom: 1px solid #BCC1CA;
            height: 94px;
            position: relative;
        }

        .essay-header {
            display: flex;
            gap: 12px;
            margin-bottom: 12px;
        }

        .essay-unit, .essay-type, .essay-format {
            font-size: 11px;
            line-height: 18px;
            color: #323842;
        }

        .essay-title {
            font-size: 11px;
            line-height: 18px;
            color: #323842;
            margin-bottom: 17px;
        }

        .essay-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .essay-time {
            font-size: 8px;
            line-height: 14px;
            color: #9095A0;
        }

        .essay-score {
            position: absolute;
            right: 17px;
            top: 29px;
            display: flex;
            align-items: baseline;
            gap: 2px;
        }

        .score-number {
            font-size: 24px;
            line-height: 36px;
            color: #DE3B40;
        }

        .score-unit {
            font-size: 12px;
            line-height: 20px;
            color: #DE3B40;
        }

        .upload-btn {
            background: #636AE8;
            border: none;
            border-radius: 4px;
            padding: 1px 8px;
            height: 20px;
            color: #FFFFFF;
            font-size: 11px;
            line-height: 18px;
            cursor: pointer;
        }

        .arrow-right {
            position: absolute;
            right: 17px;
            top: 12px;
            width: 16px;
            height: 16px;
            color: #323842;
        }

        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 375px;
            height: 48px;
            background: transparent;
            display: flex;
            padding: 0 6px;
        }

        .nav-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 4px;
            cursor: pointer;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
        }

        .nav-text {
            font-size: 10px;
            line-height: 16px;
            color: #424955;
        }

        .nav-item.active .nav-text {
            color: #4850E4;
            font-weight: 700;
        }

        .nav-item.active {
            position: relative;
        }

        .nav-item.active::after {
            content: '';
            position: absolute;
            top: 2.8px;
            right: 12px;
            width: 12px;
            height: 12px;
            background: #DE3B40;
            border-radius: 5px;
            border: 2px solid #FFFFFF;
        }

        /* 底线 */
        .bottom-line {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 375px;
            height: 4px;
            background: #F8F9FA;
            border-top: 1px solid #BCC1CA;
        }

        .end-text {
            text-align: center;
            padding: 20px;
            font-size: 8px;
            line-height: 14px;
            color: #9095A0;
            margin-bottom: 60px;
        }

        /* 选中状态样式 */
        .child-item.selected .child-avatar {
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .child-item.selected .child-name {
            font-weight: 700;
            color: #4850E4;
        }

        /* 悬停效果 */
        .child-item:hover {
            cursor: pointer;
        }

        .essay-item:hover {
            background-color: #F8F9FA;
            cursor: pointer;
        }

        .upload-btn:hover {
            background-color: #4850E4;
        }

        .nav-item:hover {
            opacity: 0.8;
        }

        /* 过渡动画 */
        .child-avatar, .child-name, .nav-item, .upload-btn {
            transition: all 0.2s ease;
        }

        /* 响应式设计 */
        @media (max-width: 375px) {
            body {
                max-width: 100%;
            }

            .bottom-nav {
                width: 100%;
            }

            .bottom-line {
                width: 100%;
            }
        }

        @media (min-width: 376px) {
            body {
                border: 1px solid #E5E5E5;
                box-shadow: 0 0 20px rgba(0,0,0,0.1);
            }
        }
    </style>
</head>
<body>
    <!-- 状态栏 -->
    <div class="status-bar">
        <div class="signal-icons">
            <div class="signal-bar" style="height: 4px;"></div>
            <div class="signal-bar" style="height: 6px;"></div>
            <div class="signal-bar" style="height: 8px;"></div>
            <div class="signal-bar" style="height: 10px;"></div>
        </div>
        <div class="battery-icon"></div>
    </div>

    <!-- 顶部用户信息 -->
    <div class="header">
        <div class="user-avatar">
            <div style="width: 100%; height: 100%; background: linear-gradient(45deg, #CED0F8, #636AE8);"></div>
        </div>
        <div class="user-name">Rachel</div>
    </div>

    <!-- 孩子选择区域 -->
    <div class="children-section">
        <div class="children-list">
            <div class="child-item">
                <div class="child-avatar child1">
                    <div style="width: 100%; height: 100%; background: linear-gradient(45deg, #D8CBF5, #7F55E0);"></div>
                </div>
                <div class="child-name">孩子1</div>
            </div>
            <div class="child-item">
                <div class="child-avatar child2">
                    <div style="width: 100%; height: 100%; background: linear-gradient(45deg, #BAF3EB, #22CCB2);"></div>
                </div>
                <div class="child-name">孩子2</div>
            </div>
            <div class="child-item">
                <div class="child-avatar child3">
                    <div style="width: 100%; height: 100%; background: linear-gradient(45deg, #F8CEDB, #E8618C);"></div>
                </div>
                <div class="child-name">孩子3</div>
            </div>
            <div class="child-item">
                <div class="add-child">
                    <svg class="add-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="12" y1="5" x2="12" y2="19"></line>
                        <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                </div>
                <div class="child-name">孩子</div>
            </div>
        </div>
    </div>

    <!-- 学生信息卡片 -->
    <div class="student-info">
        <div class="student-header">
            <div class="student-avatar">
                <div style="width: 100%; height: 100%; background: linear-gradient(45deg, #D8CBF5, #7F55E0);"></div>
            </div>
            <div class="student-details">
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span class="student-name">孩子1</span>
                    <span class="student-birth">2020.06.03</span>
                    <span class="student-age">6岁</span>
                </div>
            </div>
            <svg class="wechat-icon" viewBox="0 0 16 16" fill="currentColor">
                <path d="M8 0C3.58 0 0 3.14 0 7c0 2.31 1.91 4.38 4.78 5.28L6 14l1.22-1.72C7.48 12.28 7.74 12.28 8 12.28c4.42 0 8-3.14 8-7S12.42 0 8 0z"/>
            </svg>
        </div>
        <div class="school-info">
            <span>江头中心小学</span>
            <span>一年一班</span>
            <span>09号</span>
        </div>
    </div>

    <!-- 学年信息 -->
    <div class="semester-info">2025～2026学年 上学期</div>

    <!-- 分隔线 -->
    <div class="divider"></div>

    <!-- 作文列表 -->
    <div class="essay-list">
        <!-- 第三单元作文 -->
        <div class="essay-item">
            <div class="essay-header">
                <span class="essay-unit">第三单元</span>
                <span class="essay-type">单元作文</span>
                <span class="essay-format">全命题</span>
            </div>
            <div class="essay-title">我的植物朋友</div>
            <div class="essay-footer">
                <span class="essay-time">2025.09.30 14:59</span>
                <button class="upload-btn">上传作文</button>
            </div>
            <svg class="arrow-right" viewBox="0 0 16 16" fill="none" stroke="currentColor" stroke-width="2">
                <path d="m6 4 4 4-4 4"/>
            </svg>
        </div>

        <!-- 分隔线 -->
        <div style="height: 20px; background: #F8F9FA; border-bottom: 1px solid #BCC1CA;"></div>

        <!-- 第二单元作文 -->
        <div class="essay-item">
            <div class="essay-header">
                <span class="essay-unit">第二单元</span>
                <span class="essay-type">单元作文</span>
                <span class="essay-format">全命题</span>
            </div>
            <div class="essay-title">我的植物朋友</div>
            <div class="essay-footer">
                <span class="essay-time">2025.09.08 11:22</span>
            </div>
            <div class="essay-score">
                <span class="score-number">28</span>
                <span class="score-unit">分</span>
            </div>
            <svg class="arrow-right" viewBox="0 0 16 16" fill="none" stroke="currentColor" stroke-width="2">
                <path d="m6 4 4 4-4 4"/>
            </svg>
        </div>

        <!-- 分隔线 -->
        <div style="height: 20px; background: #F8F9FA; border-bottom: 1px solid #BCC1CA;"></div>

        <!-- 第一单元作文 -->
        <div class="essay-item">
            <div class="essay-header">
                <span class="essay-unit">第一单元</span>
                <span class="essay-type">单元作文</span>
                <span class="essay-format">全命题</span>
            </div>
            <div class="essay-title">我的植物朋友</div>
            <div class="essay-footer">
                <span class="essay-time">2025.09.08 11:22</span>
            </div>
            <div class="essay-score">
                <span class="score-number">28</span>
                <span class="score-unit">分</span>
            </div>
            <svg class="arrow-right" viewBox="0 0 16 16" fill="none" stroke="currentColor" stroke-width="2">
                <path d="m6 4 4 4-4 4"/>
            </svg>
        </div>

        <!-- 分隔线 -->
        <div style="height: 20px; background: #F8F9FA; border-bottom: 1px solid #BCC1CA;"></div>
    </div>

    <!-- 底线文字 -->
    <div class="end-text">------我是有底线的------</div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-item">
            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="#424955" stroke-width="2">
                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                <polyline points="9,22 9,12 15,12 15,22"/>
            </svg>
            <span class="nav-text">成长数据</span>
        </div>
        <div class="nav-item active">
            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="#4850E4" stroke-width="2">
                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                <polyline points="9,22 9,12 15,12 15,22"/>
            </svg>
            <span class="nav-text">首页</span>
        </div>
        <div class="nav-item">
            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="#424955" stroke-width="2">
                <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"/>
            </svg>
            <span class="nav-text">过往作文</span>
        </div>
        <div class="nav-item">
            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="#424955" stroke-width="2">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                <circle cx="12" cy="7" r="4"/>
            </svg>
            <span class="nav-text">个人中心</span>
        </div>
    </div>

    <!-- 底线 -->
    <div class="bottom-line"></div>

    <script>
        // 添加交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 孩子选择功能
            const childItems = document.querySelectorAll('.child-item');
            childItems.forEach(item => {
                item.addEventListener('click', function() {
                    // 移除其他选中状态
                    childItems.forEach(child => child.classList.remove('selected'));
                    // 添加选中状态
                    this.classList.add('selected');
                });
            });

            // 底部导航切换
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.addEventListener('click', function() {
                    // 移除其他激活状态
                    navItems.forEach(nav => nav.classList.remove('active'));
                    // 添加激活状态
                    this.classList.add('active');
                });
            });

            // 上传作文按钮
            const uploadBtns = document.querySelectorAll('.upload-btn');
            uploadBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    alert('上传作文功能');
                });
            });

            // 作文项点击
            const essayItems = document.querySelectorAll('.essay-item');
            essayItems.forEach(item => {
                item.addEventListener('click', function() {
                    alert('查看作文详情');
                });
            });
        });
    </script>
</body>
</html>
